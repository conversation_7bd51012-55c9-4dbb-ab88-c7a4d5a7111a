{
  "extends": "../../tsconfig.base.json",  // Changed to more conventional base name
  "compilerOptions": {
    "outDir": "dist",
    "rootDir": "src",
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "noEmit": false,
    "module": "ESNext",                  // More future-proof than es2022
    "target": "ES2022",                   // More widely supported than es2024
    "moduleResolution": "NodeNext",       // Preferred over node16
    
    // Added essential compiler options for library
    "composite": true,                    // Enable project references
    "esModuleInterop": true,              // Better interoperability
    "forceConsistentCasingInFileNames": true,
    "strict": true,                       // Enable all strict checks
    "skipLibCheck": true,                 // Improve compilation speed
    "jsx": "react-jsx",                   // If using React
    "lib": ["ES2022", "DOM"],             // Appropriate runtime libraries
    
    // Improved emit settings
    "emitDeclarationOnly": false,         // Ensure JS output
    "inlineSources": true,                // Better debugging
    "importHelpers": true                 // Reduce bundle size
  },
  "include": ["src/**/*"],
  "exclude": [
    "dist", 
    "node_modules", 
    "**/*.test.ts", 
    "**/*.spec.ts",
    "**/__tests__/**",                   // Additional test exclusion
    "**/__mocks__/**"                    // Mock files exclusion
  ],
  "references": [                        // If using project references
    { "path": "../common" }              // Example reference
  ]
}
